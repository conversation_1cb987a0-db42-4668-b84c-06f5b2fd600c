import React, { useState, useEffect } from 'react';
import { useThemeStore } from '../../../../stores/themeStore';
import { MessageList, MessageInput } from '../core';
import { mockMessages, mockUsers } from '../../../../mocks/data/discuss';
import type { Message, User } from '../../types';

export interface DirectMessagesViewProps {
  className?: string;
  'data-testid'?: string;
}

export const DirectMessagesView: React.FC<DirectMessagesViewProps> = ({
  className = '',
  'data-testid': testId,
}) => {
  const { colors } = useThemeStore();
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const currentUserId = '2'; // Mock current user (different from channels view)
  const otherUserId = '1'; // <PERSON>

  // Load DM messages
  useEffect(() => {
    setIsLoading(true);
    // Simulate API call - filter messages that don't have channelId (DMs)
    setTimeout(() => {
      const dmMessages = mockMessages.filter(m => !m.channelId);
      setMessages(dmMessages);
      setIsLoading(false);
    }, 500);
  }, []);

  const handleSendMessage = (content: string, attachments?: File[]) => {
    const newMessage: Message = {
      id: `msg-${Date.now()}`,
      content,
      authorId: currentUserId,
      timestamp: new Date(),
      reactions: [],
      attachments: attachments?.map(file => ({
        id: `att-${Date.now()}`,
        name: file.name,
        type: file.type.startsWith('image/') ? 'image' :
              file.type.startsWith('video/') ? 'video' :
              file.type.startsWith('audio/') ? 'audio' : 'document',
        url: URL.createObjectURL(file),
        size: file.size,
        mimeType: file.type,
      })) || [],
      mentions: [],
      isDeleted: false,
      deliveryStatus: 'sent',
    };

    setMessages(prev => [...prev, newMessage]);
  };

  const handleReaction = (messageId: string, emoji: string) => {
    setMessages(prev => prev.map(message => {
      if (message.id === messageId) {
        const existingReaction = message.reactions.find(r => r.emoji === emoji);
        if (existingReaction) {
          if (existingReaction.userIds.includes(currentUserId)) {
            // Remove reaction
            existingReaction.userIds = existingReaction.userIds.filter(id => id !== currentUserId);
            existingReaction.count--;
            if (existingReaction.count === 0) {
              message.reactions = message.reactions.filter(r => r.emoji !== emoji);
            }
          } else {
            // Add reaction
            existingReaction.userIds.push(currentUserId);
            existingReaction.count++;
          }
        } else {
          // New reaction
          message.reactions.push({
            emoji,
            userIds: [currentUserId],
            count: 1,
          });
        }
      }
      return message;
    }));
  };

  const handleTyping = (isTyping: boolean) => {
    // TODO: Implement typing indicator for DMs
    console.log('Typing in DM:', isTyping);
  };

  return (
    <div
      className={`flex-1 flex flex-col ${className}`}
      data-testid={testId}
    >
      {/* DM Header */}
      <div
        className="px-6 py-4 border-b"
        style={{
          backgroundColor: colors.surface,
          borderBottomColor: colors.border,
        }}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center text-white font-semibold">
                JD
              </div>
              <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
            <div>
              <h1 className="text-xl font-semibold" style={{ color: colors.text }}>
                John Doe
              </h1>
              <p className="text-sm" style={{ color: colors.textSecondary }}>
                🟢 Online
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="Voice call"
            >
              📞
            </button>
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="Video call"
            >
              📹
            </button>
            <button
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              style={{ color: colors.textSecondary }}
              title="User info"
            >
              ℹ️
            </button>
          </div>
        </div>
      </div>

      {/* Messages Area */}
      <MessageList
        messages={messages}
        users={mockUsers}
        currentUserId={currentUserId}
        isLoading={isLoading}
        onReaction={handleReaction}
        onReply={(messageId) => console.log('Reply to:', messageId)}
        onEdit={(messageId) => console.log('Edit:', messageId)}
        onDelete={(messageId) => console.log('Delete:', messageId)}
      />

      {/* Message Input */}
      <MessageInput
        placeholder="Type a message to John..."
        onSendMessage={handleSendMessage}
        onTyping={handleTyping}
      />
    </div>
  );
};
