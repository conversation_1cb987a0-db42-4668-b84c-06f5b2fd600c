{"name": "nexed-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "prepare": "husky", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@tailwindcss/vite": "^4.1.11", "clsx": "^2.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-international-phone": "^4.5.0", "react-router-dom": "^7.6.3", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "zustand": "^5.0.6"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.1", "@eslint/js": "^9.30.1", "@playwright/test": "^1.53.2", "@storybook/addon-a11y": "^9.0.16", "@storybook/addon-docs": "^9.0.16", "@storybook/addon-vitest": "^9.0.16", "@storybook/react-vite": "^9.0.16", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.6.0", "@vitest/browser": "3.2.4", "@vitest/coverage-v8": "3.2.4", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-storybook": "^9.0.16", "globals": "^16.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "msw": "^2.10.3", "playwright": "^1.53.2", "prettier": "^3.6.2", "storybook": "^9.0.16", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.3", "vitest": "^3.2.4", "vitest-axe": "^0.1.0"}, "msw": {"workerDirectory": ["public"]}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md}": ["prettier --write"]}}