// Discuss-specific UI components will be exported here
// These are reusable UI components specific to the discuss module

// Input components
export { EmojiPicker } from './EmojiPicker';
export type { EmojiPickerProps } from './EmojiPicker';

export { FileUpload } from './FileUpload';
export type { FileUploadProps, FilePreview } from './FileUpload';

export { RichTextEditor } from './RichTextEditor';
export type { RichTextEditorProps } from './RichTextEditor';

export { MessageSearch } from './MessageSearch';
export type { MessageSearchProps, SearchResult, SearchFilters } from './MessageSearch';

export { ContentRenderer } from './ContentRenderer';
export type { ContentRendererProps } from './ContentRenderer';

export { FilePreview } from './FilePreview';
export type { FilePreviewProps } from './FilePreview';

// User presence components
export { UserPresence, UserAvatar, TypingIndicator, OnlineUsersList } from './UserPresence';
export type { UserPresenceProps, UserAvatarProps, TypingIndicatorProps, OnlineUsersListProps } from './UserPresence';

// Mention components
export { MentionSuggestions, MentionInput } from './MentionSuggestions';
export type { MentionSuggestionsProps, MentionInputProps, MentionSuggestion } from './MentionSuggestions';

// Reaction components
export { ReactionButton, ReactionPicker, MessageReactions, ReactionSummary, ReactionTooltip } from './ReactionButton';
export type { ReactionButtonProps, ReactionPickerProps, MessageReactionsProps, ReactionSummaryProps, ReactionTooltipProps } from './ReactionButton';

// Notification components
export { NotificationSettingsPanel } from './NotificationSettings';
export type { NotificationSettingsProps } from './NotificationSettings';

// Status components
export { DeliveryStatus } from './DeliveryStatus';
export type { DeliveryStatusProps } from './DeliveryStatus';

// Message pin components
export { MessagePin, PinnedMessagesList, PinnedMessageBanner } from './MessagePin';
export type { MessagePinProps, PinnedMessagesListProps, PinnedMessageBannerProps, PinnedMessage } from './MessagePin';

// Navigation and UX components
export { QuickSwitcher } from './QuickSwitcher';
export type { QuickSwitcherProps, QuickSwitcherItem } from './QuickSwitcher';

export { ViewModeSelector, ViewModeToggle } from './ViewModeSelector';
export type { ViewModeSelectorProps, ViewModeToggleProps, ViewMode, ViewModeOption } from './ViewModeSelector';

export { ChannelOrganizer } from './ChannelOrganizer';
export type { ChannelOrganizerProps, ChannelFolder } from './ChannelOrganizer';

// Collaboration components
export { PollCreator, PollDisplay } from './PollCreator';
export type { PollCreatorProps, PollDisplayProps, Poll, PollOption } from './PollCreator';

export { MessageToTask, TaskDisplay } from './TaskIntegration';
export type { MessageToTaskProps, TaskDisplayProps, Task } from './TaskIntegration';

// Delivery status components
export { DeliveryStatus, ReadReceiptTooltip, MessageStatusIndicator } from './DeliveryStatus';
export type { DeliveryStatusProps, ReadReceiptTooltipProps, MessageStatusIndicatorProps } from './DeliveryStatus';
